import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, Subscription } from 'rxjs';
import { WebSocketService } from '../websocket/WebSocketService';
import { User, UserPageResponse } from '@app/models/backend/user';
import { Store } from '@ngrx/store';
import * as fromUser from '@app/store/user';
import { HttpClient } from '@angular/common/http';
import { environment } from '@src/environments/environment';
import { GenericResponse } from '@app/models/backend/generic-response';
import { UserResponseDTO } from '@app/models/backend/user/user-response.dto';
import { v4 as uuidv4 } from 'uuid';

/**
 * Servicio para manejar la comunicación en tiempo real de usuarios mediante WebSockets
 */
@Injectable({
  providedIn: 'root',
})
export class UserWsService implements OnDestroy {
  // Subjects para los usuarios y la paginación
  private usersSubject = new BehaviorSubject<UserResponseDTO[]>([]);
  private paginationSubject = new BehaviorSubject<{
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }>({
    totalItems: 0,
    totalPages: 0,
    currentPage: 0,
    pageSize: 10,
  });

  // Identificador único de sesión para filtrar actualizaciones
  private sessionId: string = uuidv4();

  // Último request ID para identificar respuestas a solicitudes específicas
  private lastRequestId: string = '';

  // Indicador de inicialización
  private initialized = false;
  private initializing = false;

  // Suscripciones
  private subscriptions: Subscription[] = [];

  constructor(
    private webSocketService: WebSocketService,
    private store: Store,
    private http: HttpClient
  ) {
    // Inicializar automáticamente si hay un token
    if (localStorage.getItem('token')) {
      this.initialize();
    }
  }

  ngOnDestroy(): void {
    this.clearSubscriptions();
  }

  /**
   * Inicializa el servicio y configura las suscripciones WebSocket
   */
  initialize(): void {
    if (this.initialized || this.initializing) {
      return;
    }

    this.initializing = true;

    // Verificar si el WebSocket está conectado
    if (!this.webSocketService.isConnected()) {
      this.webSocketService.connect();
    }

    // Suscribirse a los mensajes WebSocket
    this.setupSubscriptions();

    // Solicitar la lista de usuarios inmediatamente si el WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      // Pequeño retraso para asegurar que las suscripciones estén listas
      setTimeout(() => {
        this.requestUsers(0, 10);
      }, 500);
    } else {
      this.loadUsersByHttp(0, 10);
    }

    // Marcar como inicializado
    this.initialized = true;
    this.initializing = false;
  }

  /**
   * Configura las suscripciones a los canales WebSocket
   */
  private setupSubscriptions(): void {
    // Limpiar suscripciones existentes
    this.clearSubscriptions();

    // Suscribirse al tipo de mensaje USERS_LIST (evitamos duplicar suscripciones)
    this.subscriptions.push(
      this.webSocketService
        .getMessagesByType('USERS_LIST')
        .subscribe((response) => this.processUserListResponse(response))
    );

    // Asegurarse de que estamos suscritos al tópico en el servidor
    if (this.webSocketService.isConnected()) {
      this.webSocketService.subscribeToDynamicTopic(
        '/topic/users/list',
        'USERS_LIST'
      );
    }

    // Suscribirse a las actualizaciones de usuarios individuales
    this.subscriptions.push(
      this.webSocketService
        .getMessagesByType('USERS_UPDATED')
        .subscribe((message) => this.handleUserUpdate(message))
    );

    // Suscribirse a las creaciones de usuarios
    this.subscriptions.push(
      this.webSocketService
        .getMessagesByType('USER_CREATED')
        .subscribe((message) => this.handleUserCreated(message))
    );

    // Suscribirse a las eliminaciones de usuarios
    this.subscriptions.push(
      this.webSocketService
        .getMessagesByType('USER_DELETED')
        .subscribe((message) => this.handleUserDeleted(message))
    );
  }

  /**
   * Procesa la respuesta de la lista de usuarios
   * @param response Respuesta del servidor
   */
  private processUserListResponse(response: any): void {
    // Verificar si la respuesta tiene la nueva estructura con paginación
    if (response && typeof response === 'object' && 'users' in response) {
      // Nueva estructura con paginación
      const paginatedResponse = response as {
        users: UserResponseDTO[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
        pageSize: number;
        source?: string; // Campo opcional para identificar el origen de la actualización
        sessionId?: string; // Identificador de sesión para filtrar actualizaciones
        requestId?: string; // Identificador de solicitud específica
      };

      // Verificar si esta respuesta corresponde a nuestra sesión o es una respuesta a nuestra solicitud específica
      const isOurSession = paginatedResponse.sessionId === this.sessionId;
      const isOurRequest = paginatedResponse.requestId === this.lastRequestId;

      // Si la respuesta no tiene sessionId o requestId, asumimos que es una actualización global
      // que debemos procesar (compatibilidad con versiones anteriores)
      const isGlobalUpdate =
        !paginatedResponse.sessionId && !paginatedResponse.requestId;

      // Solo procesar si es nuestra sesión, nuestra solicitud específica, o una actualización global
      if (isOurSession || isOurRequest || isGlobalUpdate) {
        if (Array.isArray(paginatedResponse.users)) {
          // Obtener la información de paginación actual
          const currentPagination = this.paginationSubject.value;

          // Verificar si esta respuesta es una actualización de estado de usuario
          const isUserStatusUpdate =
            paginatedResponse.source === 'user_status_update';

          // Verificar si esta respuesta corresponde a la página solicitada explícitamente
          // o si es la primera carga
          const isExplicitPageRequest =
            (isOurRequest || isOurSession) &&
            (paginatedResponse.currentPage === currentPagination.currentPage ||
              // Si es la primera carga (currentPage es 0 por defecto)
              currentPagination.totalItems === 0);

          // Actualizar la lista de usuarios solo si es nuestra sesión o solicitud
          if (
            isOurSession ||
            isOurRequest ||
            (isGlobalUpdate && isUserStatusUpdate)
          ) {
            this.usersSubject.next(paginatedResponse.users);
          }

          // Actualizar información de paginación
          if (isUserStatusUpdate) {
            // Si es una actualización de estado de usuario, mantener la página actual
            console.log(
              'Actualización de estado de usuario recibida. Manteniendo página actual:',
              this.paginationSubject.value.currentPage
            );
            this.paginationSubject.next({
              totalItems: paginatedResponse.totalItems || 0,
              totalPages: paginatedResponse.totalPages || 0,
              currentPage: currentPagination.currentPage,
              pageSize: currentPagination.pageSize,
            });
          } else if (isExplicitPageRequest) {
            // Si es una solicitud explícita o primera carga, actualizar toda la información
            // Asegurarse de que el tamaño de página sea un número válido
            const pageSize =
              typeof paginatedResponse.pageSize === 'number' &&
              !isNaN(paginatedResponse.pageSize) &&
              paginatedResponse.pageSize > 0
                ? paginatedResponse.pageSize
                : currentPagination.pageSize;

            console.log(
              `Actualizando información de paginación: página ${paginatedResponse.currentPage}, tamaño ${pageSize}, total ${paginatedResponse.totalItems}`
            );

            this.paginationSubject.next({
              totalItems: paginatedResponse.totalItems || 0,
              totalPages: paginatedResponse.totalPages || 0,
              currentPage: paginatedResponse.currentPage,
              pageSize: pageSize,
            });
          } else if (isGlobalUpdate) {
            // Si es una actualización global, solo actualizamos el total de items y páginas
            // pero mantenemos la página actual
            this.paginationSubject.next({
              totalItems: paginatedResponse.totalItems || 0,
              totalPages: paginatedResponse.totalPages || 0,
              currentPage: currentPagination.currentPage,
              pageSize: currentPagination.pageSize,
            });
          }
          // Si no es nuestra sesión ni solicitud, ignoramos la actualización
          // para evitar afectar a otros usuarios
        }
      } else {
        // Si no es nuestra sesión ni solicitud, ignoramos la actualización
        console.log(
          'Ignorando actualización de otra sesión:',
          paginatedResponse.sessionId
        );
      }
    } else if (Array.isArray(response)) {
      // Formato antiguo (solo array de usuarios)
      // Solo actualizar si no estamos filtrando por sesión (compatibilidad con versiones anteriores)
      this.usersSubject.next(response);
    }
  }

  /**
   * Limpia todas las suscripciones
   */
  private clearSubscriptions(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions = [];
  }

  /**
   * Maneja la actualización de un usuario
   */
  private handleUserUpdate(updatedUser: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;
    const index = currentUsers.findIndex((u) => u.id === updatedUser.id);

    if (index !== -1) {
      // Actualizar el usuario existente
      const updatedUsers = [...currentUsers];
      updatedUsers[index] = updatedUser;
      this.usersSubject.next(updatedUsers);
    }
  }

  /**
   * Maneja la creación de un usuario
   */
  private handleUserCreated(newUser: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;

    // Verificar si el usuario ya existe
    if (!currentUsers.some((u) => u.id === newUser.id)) {
      // Agregar el nuevo usuario
      this.usersSubject.next([...currentUsers, newUser]);
    }
  }

  /**
   * Maneja la eliminación de un usuario
   */
  private handleUserDeleted(deletedUser: UserResponseDTO): void {
    const currentUsers = this.usersSubject.value;

    // Filtrar el usuario eliminado
    const updatedUsers = currentUsers.filter((u) => u.id !== deletedUser.id);

    if (updatedUsers.length !== currentUsers.length) {
      this.usersSubject.next(updatedUsers);
    }
  }

  /**
   * Obtiene la lista de usuarios con actualizaciones en tiempo real
   */
  getUsers(): Observable<UserResponseDTO[]> {
    return this.usersSubject.asObservable();
  }

  /**
   * Obtiene la información de paginación
   */
  getPagination(): Observable<{
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  }> {
    return this.paginationSubject.asObservable();
  }

  /**
   * Solicita la lista de usuarios al servidor
   */
  requestUsers(
    page: number = 0,
    size: number = 10,
    sedeId?: number,
    soloConectados?: boolean
  ): void {
    // Asegurarse de que los parámetros sean números válidos
    page = typeof page === 'number' && !isNaN(page) ? page : 0;
    size = typeof size === 'number' && !isNaN(size) && size > 0 ? size : 10;

    console.log(
      `UserWsService.requestUsers: Solicitando usuarios - página ${page}, tamaño ${size}, sede ${
        sedeId || 'todas'
      }`
    );

    // Guardar la página solicitada en la información de paginación actual
    // para poder identificar si una respuesta corresponde a esta solicitud específica
    const currentPagination = this.paginationSubject.value;

    // Actualizar la información de paginación con la página solicitada
    // pero mantener el resto de la información
    this.paginationSubject.next({
      ...currentPagination,
      currentPage: page,
      pageSize: size,
    });

    // Verificar si el WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      // Generar un nuevo requestId para esta solicitud específica
      this.lastRequestId = uuidv4();

      // Crear el payload con los parámetros de paginación
      const payload: any = {
        page: page,
        size: size,
        sessionId: this.sessionId, // Identificador de sesión para filtrar actualizaciones
        requestId: this.lastRequestId, // Identificador de solicitud específica
      };

      // Añadir sede_id al payload si se proporciona
      if (sedeId) {
        payload.sede_id = sedeId;
      }

      // Añadir soloConectados al payload si se proporciona
      if (soloConectados !== undefined) {
        payload.soloConectados = soloConectados;
      }

      console.log(
        `UserWsService: Enviando solicitud WebSocket con payload:`,
        payload
      );
      this.webSocketService.sendMessage('/app/users.list', payload);

      // Verificar si estamos suscritos al tópico de respuesta
      if (
        !this.webSocketService['activeSubscriptions'].has('/topic/users/list')
      ) {
        this.webSocketService.subscribeToDynamicTopic(
          '/topic/users/list',
          'USERS_LIST'
        );
      }
    } else {
      console.log(`UserWsService: WebSocket no disponible, usando HTTP`);
      this.loadUsersByHttp(page, size, sedeId);
    }
  }

  /**
   * Carga la lista de usuarios mediante HTTP
   */
  private loadUsersByHttp(
    page: number = 0,
    size: number = 10,
    sedeId?: number,
    soloConectados?: boolean
  ): void {
    // Construir la URL base
    let url = `${environment.url}api/user/listar?page=${page}&size=${size}`;

    // Añadir sede_id a la URL si se proporciona
    if (sedeId) {
      url += `&sede_id=${sedeId}`;
    }

    // Añadir soloConectados a la URL si se proporciona
    if (soloConectados !== undefined) {
      url += `&soloConectados=${soloConectados}`;
    }

    // Obtener la información de paginación actual
    const currentPagination = this.paginationSubject.value;

    this.http.get<GenericResponse<UserPageResponse>>(url).subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data && response.data.users) {
          // Convertir a UserResponseDTO[] si es necesario
          this.usersSubject.next(
            response.data.users as unknown as UserResponseDTO[]
          );

          // Actualizar información de paginación si está disponible
          if (response.data.totalItems !== undefined) {
            // Verificar si esta respuesta corresponde a la página solicitada explícitamente
            const isExplicitPageRequest =
              page === currentPagination.currentPage ||
              // Si es la primera carga (totalItems es 0 por defecto)
              currentPagination.totalItems === 0;

            if (isExplicitPageRequest) {
              // Asegurarse de que el tamaño de página sea un número válido
              const pageSize =
                typeof size === 'number' && !isNaN(size) && size > 0
                  ? size
                  : currentPagination.pageSize;

              console.log(
                `HTTP: Actualizando información de paginación: página ${page}, tamaño ${pageSize}, total ${response.data.totalItems}`
              );

              this.paginationSubject.next({
                totalItems: response.data.totalItems,
                totalPages:
                  response.data.totalPages ||
                  Math.ceil(response.data.totalItems / pageSize),
                currentPage: page,
                pageSize: pageSize,
              });
            } else {
              // Si no corresponde a la página solicitada, mantener la página actual
              this.paginationSubject.next({
                totalItems: response.data.totalItems,
                totalPages:
                  response.data.totalPages ||
                  Math.ceil(response.data.totalItems / (size || 10)),
                currentPage: currentPagination.currentPage,
                pageSize: currentPagination.pageSize,
              });
            }
          }
        }
      },
      error: () => {
        // Error silencioso
      },
    });
  }

  /**
   * Busca usuarios mediante WebSocket
   * @param query Término de búsqueda
   * @param page Número de página
   * @param size Tamaño de página
   * @param sedeId ID de la sede (opcional)
   * @param soloConectados Filtrar solo usuarios conectados (opcional)
   */
  searchUsers(
    query: string,
    page: number = 0,
    size: number = 10,
    sedeId?: number,
    soloConectados?: boolean
  ): void {
    // Asegurarse de que los parámetros sean números válidos
    page = typeof page === 'number' && !isNaN(page) ? page : 0;
    size = typeof size === 'number' && !isNaN(size) && size > 0 ? size : 10;

    console.log(
      `UserWsService.searchUsers: Buscando usuarios - query "${query}", página ${page}, tamaño ${size}, sede ${
        sedeId || 'todas'
      }`
    );

    // Guardar la página solicitada en la información de paginación actual
    const currentPagination = this.paginationSubject.value;

    // Actualizar la información de paginación con la página solicitada
    // pero mantener el resto de la información
    this.paginationSubject.next({
      ...currentPagination,
      currentPage: page,
      pageSize: size,
    });

    // Verificar si el WebSocket está conectado
    if (this.webSocketService.isConnected()) {
      // Generar un nuevo requestId para esta solicitud específica
      this.lastRequestId = uuidv4();

      // Crear el payload con los parámetros de búsqueda y paginación
      // Asegurarse de que el término de búsqueda se envíe correctamente para búsquedas por nombre completo
      const payload: any = {
        query: query.trim(), // Eliminar espacios en blanco al inicio y final, pero mantener mayúsculas/minúsculas
        page: page,
        size: size,
        sessionId: this.sessionId, // Identificador de sesión para filtrar actualizaciones
        requestId: this.lastRequestId, // Identificador de solicitud específica
      };

      // Registrar la búsqueda para depuración
      console.log(
        `UserWsService: Búsqueda por nombre completo: "${query.trim()}"`
      );

      // Añadir sede_id al payload si se proporciona
      if (sedeId) {
        payload.sede_id = sedeId;
      }

      // Añadir soloConectados al payload si se proporciona
      if (soloConectados !== undefined) {
        payload.soloConectados = soloConectados;
      }

      console.log(
        `UserWsService: Enviando búsqueda WebSocket con payload:`,
        payload
      );

      // Enviar mensaje al servidor para buscar usuarios
      this.webSocketService.sendMessage('/app/users.search', payload);

      // Verificar si estamos suscritos al tópico de respuesta
      if (
        !this.webSocketService['activeSubscriptions'].has('/topic/users/list')
      ) {
        this.webSocketService.subscribeToDynamicTopic(
          '/topic/users/list',
          'USERS_LIST'
        );
      }
    } else {
      console.log(
        `UserWsService: WebSocket no disponible, usando HTTP para búsqueda`
      );
      // Si el WebSocket no está conectado, usar HTTP
      this.searchUsersByHttp(query, page, size, sedeId);
    }
  }

  /**
   * Busca usuarios mediante HTTP
   * @param query Término de búsqueda
   * @param page Número de página
   * @param size Tamaño de página
   * @param sedeId ID de la sede (opcional)
   */
  private searchUsersByHttp(
    query: string,
    page: number = 0,
    size: number = 10,
    sedeId?: number
  ): void {
    // Construir la URL base
    let url = `${environment.url}api/user/buscar?page=${page}&size=${size}`;

    // Añadir el parámetro de búsqueda si existe
    // Asegurarse de que el término de búsqueda se envíe correctamente para búsquedas por nombre completo
    if (query && query.trim() !== '') {
      const trimmedQuery = query.trim();
      url += `&query=${encodeURIComponent(trimmedQuery)}`;

      // Registrar la búsqueda para depuración
      console.log(
        `UserWsService: Búsqueda HTTP por nombre completo: "${trimmedQuery}"`
      );
    }

    // Añadir sede_id a la URL si se proporciona
    if (sedeId) {
      url += `&sede_id=${sedeId}`;
    }

    // Obtener la información de paginación actual
    const currentPagination = this.paginationSubject.value;

    this.http.get<GenericResponse<any>>(url).subscribe({
      next: (response) => {
        if (response.rpta === 1 && response.data && response.data.users) {
          // Convertir a UserResponseDTO[] si es necesario
          this.usersSubject.next(
            response.data.users as unknown as UserResponseDTO[]
          );

          // Actualizar información de paginación si está disponible
          if (response.data.totalItems !== undefined) {
            // Verificar si esta respuesta corresponde a la página solicitada explícitamente
            const isExplicitPageRequest =
              page === currentPagination.currentPage ||
              // Si es la primera carga (totalItems es 0 por defecto)
              currentPagination.totalItems === 0;

            if (isExplicitPageRequest) {
              // Asegurarse de que el tamaño de página sea un número válido
              const pageSize =
                typeof size === 'number' && !isNaN(size) && size > 0
                  ? size
                  : currentPagination.pageSize;

              console.log(
                `HTTP: Actualizando información de paginación: página ${page}, tamaño ${pageSize}, total ${response.data.totalItems}`
              );

              this.paginationSubject.next({
                totalItems: response.data.totalItems,
                totalPages:
                  response.data.totalPages ||
                  Math.ceil(response.data.totalItems / pageSize),
                currentPage: page,
                pageSize: pageSize,
              });
            } else {
              // Si no corresponde a la página solicitada, mantener la página actual
              this.paginationSubject.next({
                totalItems: response.data.totalItems,
                totalPages:
                  response.data.totalPages ||
                  Math.ceil(response.data.totalItems / (size || 10)),
                currentPage: currentPagination.currentPage,
                pageSize: currentPagination.pageSize,
              });
            }
          }
        }
      },
      error: () => {
        // Error silencioso
      },
    });
  }

  /**
   * Crea un nuevo usuario
   * @param user Datos del usuario a crear
   */
  createUser(user: any): void {
    // Primero se crea mediante HTTP a través del store
    // El WebSocket notificará a todos los clientes cuando se cree
    this.store.dispatch(new fromUser.RegisterUser(user));
  }

  /**
   * Actualiza un usuario existente
   * @param id ID del usuario a actualizar
   * @param user Datos actualizados del usuario
   */
  updateUser(id: number, user: Partial<User>): void {
    // Primero se actualiza mediante HTTP a través del store
    // El WebSocket notificará a todos los clientes cuando se actualice
    this.store.dispatch(new fromUser.UpdateUser(id, user));
  }

  /**
   * Elimina un usuario
   * @param id ID del usuario a eliminar
   */
  deleteUser(id: number): void {
    // Primero se elimina mediante HTTP a través del store
    // El WebSocket notificará a todos los clientes cuando se elimine
    this.store.dispatch(new fromUser.DeleteUser(id));
  }
}
