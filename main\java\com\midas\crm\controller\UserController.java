package com.midas.crm.controller;

import com.midas.crm.entity.DTO.user.UserCreateDTO;
import com.midas.crm.entity.DTO.user.UserDTO;
import com.midas.crm.entity.DTO.user.UserPageDTO;
import com.midas.crm.entity.DTO.user.UserResponseDTO;
import com.midas.crm.entity.DTO.user.UserUpdateDTO;
import com.midas.crm.entity.DTO.websocket.UserStatusDTO;
import com.midas.crm.entity.DTO.websocket.UserWebSocketDTO;
import com.midas.crm.entity.Role;
import com.midas.crm.entity.Sede;
import com.midas.crm.entity.User;
import com.midas.crm.mapper.UserMapper;
import com.midas.crm.repository.SedeRepository;
import com.midas.crm.security.UserPrincipal;
import com.midas.crm.service.ExcelService;
import com.midas.crm.service.UserConnectionService;
import com.midas.crm.service.UserService;
import com.midas.crm.utils.GenericResponse;
import com.midas.crm.utils.MidasErrorMessage;
import com.midas.crm.utils.ResponseBuilder;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@Slf4j
@RequestMapping("${api.route.user}")
public class UserController {
    private final UserService userService;
    private final UserConnectionService userConnectionService;
    private final SimpMessagingTemplate messagingTemplate;
    private final ExcelService excelService;
    private final SedeRepository sedeRepository;

    public UserController(UserService userService, UserConnectionService userConnectionService,
            SimpMessagingTemplate messagingTemplate, ExcelService excelService,
            SedeRepository sedeRepository) {
        this.userService = userService;
        this.userConnectionService = userConnectionService;
        this.messagingTemplate = messagingTemplate;
        this.excelService = excelService;
        this.sedeRepository = sedeRepository;
    }

    /**
     * Crea un nuevo usuario y notifica a todos los clientes conectados
     *
     * @param createDTO DTO con los datos para crear el usuario
     * @return Respuesta con el usuario creado
     */
    @PostMapping("/registrar")
    @Transactional
    public ResponseEntity<GenericResponse<UserResponseDTO>> createUser(@Valid @RequestBody UserCreateDTO createDTO) {
        // Verificar si se ha proporcionado un coordinador para un asesor
        if (createDTO.getCoordinador_id() == null && createDTO.getRole() == Role.ASESOR) {
            // log.warn("Se está creando un asesor sin coordinador asignado: {}",
            // createDTO.getUsername());
            // Aquí podrías implementar una lógica para asignar un coordinador
            // automáticamente
        }

        try {
            return Optional.of(createDTO)
                    .map(UserMapper::toEntity)
                    .map(userService::createUser)
                    .map(UserMapper::toResponseDTO)
                    .map(responseDTO -> {
                        // Notificar a todos los clientes conectados sobre el nuevo usuario
                        UserWebSocketDTO wsResponse = UserWebSocketDTO.created(responseDTO);
                        messagingTemplate.convertAndSend("/topic/users", wsResponse);
                        return ResponseBuilder.created(responseDTO, "Usuario creado exitosamente");
                    })
                    .orElseThrow(() -> new RuntimeException("Error al crear el usuario"));
        } catch (Exception e) {
            // log.error("Error al crear el usuario: {}", e.getMessage());
            return ResponseBuilder.error("Error al crear el usuario: " + e.getMessage());
        }
    }

    @PostMapping("/importar-excel")
    public ResponseEntity<GenericResponse<List<User>>> importarUsuariosDesdeExcel(
            @RequestParam("file") MultipartFile file) {
        try {
            List<User> usuarios = excelService.leerUsuariosDesdeExcel(file, Role.ASESOR);

            // Usar programación funcional para procesar la lista de usuarios
            List<User> usuariosGuardados = usuarios.stream()
                    .map(usuario -> {
                        try {
                            return userService.createUser(usuario);
                        } catch (Exception e) {
                            // Si hay un error con un usuario específico, continuamos con los demás
                            // log.error("Error al guardar usuario: " + e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Verificar si se guardó algún usuario
            return usuariosGuardados.isEmpty()
                    ? ResponseBuilder.error(MidasErrorMessage.USUARIO_IMPORT_ERROR.getErrorMessage())
                    : ResponseBuilder.success(usuariosGuardados,
                            MidasErrorMessage.USUARIO_IMPORT_SUCCESS.getErrorMessage());
        } catch (Exception e) {
            // log.error("Error al importar usuarios desde Excel: " + e.getMessage());
            return ResponseBuilder.error(e.getMessage());
        }
    }

    @PostMapping("/importar-excel-backoffice")
    public ResponseEntity<GenericResponse<List<User>>> importarUsuariosBackofficeDesdeExcel(
            @RequestParam("file") MultipartFile file) {
        try {
            List<User> usuarios = excelService.leerUsuariosDesdeExcel(file, Role.BACKOFFICE);

            // Usar programación funcional para procesar la lista de usuarios
            List<User> usuariosGuardados = usuarios.stream()
                    .map(usuario -> {
                        try {
                            return userService.createUser(usuario);
                        } catch (Exception e) {
                            // Si hay un error con un usuario específico, continuamos con los demás
                            // log.error("Error al guardar usuario backoffice: " + e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // Verificar si se guardó algún usuario
            return usuariosGuardados.isEmpty()
                    ? ResponseBuilder.error(MidasErrorMessage.USUARIO_IMPORT_ERROR.getErrorMessage())
                    : ResponseBuilder.success(usuariosGuardados,
                            MidasErrorMessage.USUARIO_IMPORT_SUCCESS.getErrorMessage());
        } catch (Exception e) {
            // log.error("Error al importar usuarios backoffice desde Excel: " +
            // e.getMessage());
            return ResponseBuilder.error(e.getMessage());
        }
    }

    // Endpoint para listar usuarios con filtro opcional por sede_id y estado de
    // conexión
    @GetMapping("listar")
    public ResponseEntity<GenericResponse<UserPageDTO>> listUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long sede_id,
            @RequestParam(required = false, defaultValue = "false") boolean soloConectados) {
        return userService.listUsers(page, size, sede_id, soloConectados);
    }

    @GetMapping
    public ResponseEntity<GenericResponse<Map<String, Object>>> getCurrentUser(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            HttpServletRequest request) {
        return userService.getCurrentUser(userPrincipal, request);
    }

    /**
     * Obtiene la lista de roles disponibles en el sistema
     *
     * @return Lista de roles disponibles
     */
    @GetMapping("/roles")
    public ResponseEntity<List<String>> getRoles() {
        return Arrays.stream(Role.values())
                .map(Enum::name)
                .collect(Collectors.collectingAndThen(
                        Collectors.toList(),
                        roles -> ResponseEntity.ok(roles)));
    }

    /**
     * Obtiene un usuario por su ID
     *
     * @param userId ID del usuario a obtener
     * @return Respuesta con el usuario encontrado
     */
    @GetMapping("/{userId}")
    public ResponseEntity<GenericResponse<UserResponseDTO>> getUserById(@PathVariable Long userId) {
        return Optional.ofNullable(userService.findUserById(userId))
                .map(UserMapper::toResponseDTO)
                .map(dto -> ResponseBuilder.success(dto, "Usuario encontrado"))
                .orElse(ResponseBuilder.notFound("Usuario no encontrado"));
    }

    /**
     * Elimina un usuario (baja lógica) y notifica a todos los clientes conectados
     *
     * @param userId ID del usuario a eliminar
     * @return Respuesta con el resultado de la operación
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<GenericResponse<Void>> deleteUser(@PathVariable Long userId) {
        try {
            User user = userService.findUserById(userId);
            if (user == null) {
                return ResponseBuilder.notFound("Usuario no encontrado");
            }

            // Convertir a DTO de respuesta antes de eliminar (para notificar)
            UserResponseDTO responseDTO = UserMapper.toResponseDTO(user);

            // Eliminar el usuario y notificar si fue exitoso
            return userService.deleteUser(userId)
                    ? notifyUserDeletion(responseDTO)
                    : ResponseBuilder.error("No se pudo eliminar el usuario");
        } catch (Exception e) {
            // log.error("Error al eliminar el usuario: {}", e.getMessage());
            return ResponseBuilder.error("Error al eliminar el usuario: " + e.getMessage());
        }
    }

    /**
     * Notifica a todos los clientes conectados sobre la eliminación de un usuario
     *
     * @param responseDTO DTO del usuario eliminado
     * @return Respuesta con el resultado de la operación
     */
    private ResponseEntity<GenericResponse<Void>> notifyUserDeletion(UserResponseDTO responseDTO) {
        // Notificar a todos los clientes conectados sobre la eliminación del usuario
        UserWebSocketDTO wsResponse = UserWebSocketDTO.deleted(responseDTO);
        messagingTemplate.convertAndSend("/topic/users", wsResponse);

        return ResponseBuilder.success(null, "Usuario eliminado exitosamente");
    }

    /**
     * Endpoint legacy para mantener compatibilidad
     */
    @DeleteMapping("/soft/{userId}")
    public ResponseEntity<GenericResponse<Void>> softDeleteUser(@PathVariable Long userId) {
        try {
            return Optional.of(userId)
                    .map(id -> {
                        // log.info("Eliminando usuario (endpoint legacy) con ID: {}", id);
                        return userService.deleteUserById(id);
                    })
                    .orElseGet(() -> ResponseBuilder.error("ID de usuario inválido"));
        } catch (Exception e) {
            // log.error("Error al eliminar el usuario: {}", e.getMessage());
            return ResponseBuilder.error("Error al eliminar el usuario: " + e.getMessage());
        }
    }

    // Endpoint para buscar usuarios con filtro opcional por sede_id
    @GetMapping("/buscar")
    public ResponseEntity<GenericResponse<Map<String, Object>>> searchUsers(
            @RequestParam(required = false) String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) Long sede_id) {
        return userService.searchUsers(query, page, size, sede_id);
    }

    /**
     * Actualiza un usuario existente y notifica a todos los clientes conectados
     *
     * @param userId    ID del usuario a actualizar
     * @param updateDTO DTO con los datos para actualizar el usuario
     * @return Respuesta con el usuario actualizado
     */
    @PutMapping("/{userId}")
    public ResponseEntity<GenericResponse<UserResponseDTO>> updateUser(
            @PathVariable Long userId,
            @Valid @RequestBody UserUpdateDTO updateDTO) {
        try {
            // Obtener el usuario existente
            User existingUser = userService.findUserById(userId);
            if (existingUser == null) {
                return ResponseBuilder.notFound("Usuario no encontrado");
            }

            // Verificar si se está actualizando un asesor sin coordinador
            if (updateDTO.getCoordinador_id() == null && !updateDTO.isRemoveCoordinador() &&
                    existingUser.getRole() == Role.ASESOR && existingUser.getCoordinador() == null) {
                // log.warn("Se está actualizando un asesor sin asignarle coordinador: {}",
                // existingUser.getUsername());
            }

            // Actualizar el usuario con los datos del DTO
            return updateUserAndNotify(userId, existingUser, updateDTO);
        } catch (Exception e) {
            // log.error("Error al actualizar el usuario: {}", e.getMessage());
            return ResponseBuilder.error("Error al actualizar el usuario: " + e.getMessage());
        }
    }

    /**
     * Actualiza un usuario y notifica a todos los clientes conectados
     *
     * @param userId       ID del usuario a actualizar
     * @param existingUser Usuario existente
     * @param updateDTO    DTO con los datos para actualizar el usuario
     * @return Respuesta con el usuario actualizado
     */
    private ResponseEntity<GenericResponse<UserResponseDTO>> updateUserAndNotify(
            Long userId, User existingUser, UserUpdateDTO updateDTO) {

        // Actualizar el usuario con los datos del DTO
        UserMapper.updateUserFromDTO(existingUser, updateDTO);

        // Si se proporciona una contraseña, encriptarla
        if (updateDTO.getPassword() != null && !updateDTO.getPassword().isEmpty()) {
            existingUser.setPassword(updateDTO.getPassword());
        }

        // Si se actualizó la sede, cargar el nombre de la sede
        updateUserSede(existingUser, updateDTO.getSede_id());

        // Guardar los cambios
        User updatedUser = userService.updateUser(userId, existingUser);

        // Cargar la sede completa para la respuesta
        loadFullSede(updatedUser);

        // Convertir a DTO de respuesta
        UserResponseDTO responseDTO = UserMapper.toResponseDTO(updatedUser);

        // Notificar a todos los clientes conectados sobre la actualización del usuario
        UserWebSocketDTO wsResponse = UserWebSocketDTO.updated(responseDTO);
        messagingTemplate.convertAndSend("/topic/users", wsResponse);

        return ResponseBuilder.success(responseDTO, "Usuario actualizado exitosamente");
    }

    /**
     * Actualiza la sede de un usuario
     *
     * @param user   Usuario a actualizar
     * @param sedeId ID de la sede
     */
    private void updateUserSede(User user, Long sedeId) {
        if (sedeId != null) {
            sedeRepository.findById(sedeId)
                    .ifPresent(sede -> {
                        user.setSede(sede);
                        user.setSedeNombre(sede.getNombre());
                    });
        }
    }

    /**
     * Carga la sede completa para un usuario
     *
     * @param user Usuario para el que cargar la sede
     */
    private void loadFullSede(User user) {
        Optional.ofNullable(user.getSede())
                .map(Sede::getId)
                .flatMap(sedeRepository::findById)
                .ifPresent(user::setSede);
    }

    /**
     * Endpoint legacy para mantener compatibilidad
     */
    @PutMapping("/legacy/{userId}")
    public ResponseEntity<GenericResponse<UserResponseDTO>> updateUserLegacy(
            @PathVariable Long userId,
            @RequestBody User user) {
        try {
            // log.info("Actualizando usuario (endpoint legacy) con ID: {}", userId);
            return Optional.ofNullable(user)
                    .map(u -> userService.updateUser(userId, u))
                    .map(UserMapper::toResponseDTO)
                    .map(dto -> ResponseBuilder.success(dto, "Usuario actualizado exitosamente"))
                    .orElseThrow(() -> new RuntimeException("Error al actualizar el usuario: datos inválidos"));
        } catch (Exception e) {
            // log.error("Error al actualizar el usuario: {}", e.getMessage());
            return ResponseBuilder.error("Error al actualizar el usuario: " + e.getMessage());
        }
    }

    /**
     * Endpoint WebSocket para obtener el estado de los usuarios
     */
    @MessageMapping("/users.status")
    @SendTo("/topic/users/status/all")
    public List<UserStatusDTO> getUserStatusWs() {
        // Reducir logs para evitar saturación
        // log.info("Solicitud WebSocket para obtener estado de todos los usuarios");

        try {
            // Obtener la lista de estados de todos los usuarios y procesarla
            return Optional.of(userConnectionService.getAllUserStatuses())
                    .map(statuses -> {
                        // Enviar una notificación consolidada con todos los datos
                        sendStatusNotification(statuses, "USERS_STATUS_UPDATED");

                        // Solo registrar logs cuando hay un número significativo de usuarios
                        if (statuses.size() > 10) {
                            // log.info("Estado de {} usuarios enviado en una sola actualización",
                            // statuses.size());
                        }

                        return statuses;
                    })
                    .orElseGet(ArrayList::new);
        } catch (Exception e) {
            // log.error("Error al obtener estado de usuarios: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Envía una notificación con el estado de los usuarios
     *
     * @param statuses Lista de estados de usuarios
     * @param type     Tipo de notificación
     */
    private void sendStatusNotification(List<UserStatusDTO> statuses, String type) {
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", type);
        notification.put("count", statuses.size());
        notification.put("timestamp", new Date());
        notification.put("payload", statuses); // Incluir la lista completa en la notificación
        messagingTemplate.convertAndSend("/topic/notifications", notification);
    }

    /**
     * Endpoint WebSocket adicional para obtener el estado de todos los usuarios
     * Este endpoint es explícitamente llamado por el cliente para asegurar que
     * todos los usuarios vean a todos los demás
     */
    @MessageMapping("/users.status.all")
    @SendTo("/topic/users/status/all")
    public List<UserStatusDTO> getAllUsersStatusWs() {
        // log.info("Solicitud WebSocket explícita para obtener estado de TODOS los
        // usuarios");

        try {
            // Obtener la lista de estados de todos los usuarios y procesarla
            return Optional.of(userConnectionService.getAllUserStatuses())
                    .map(statuses -> {
                        // Enviar también a /topic/users/status para asegurar que todos los clientes
                        // reciban la actualización
                        messagingTemplate.convertAndSend("/topic/users/status", statuses);

                        // Enviar una notificación consolidada con todos los datos
                        sendStatusNotification(statuses, "USERS_STATUS_UPDATED");

                        // log.info("Estado de {} usuarios enviado en una sola actualización (solicitud
                        // explícita)", statuses.size());

                        return statuses;
                    })
                    .orElseGet(ArrayList::new);
        } catch (Exception e) {
            // log.error("Error al obtener estado de todos los usuarios: {}",
            // e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    // Mapa para evitar procesar conexiones duplicadas
    private final Map<Long, Long> processedConnections = new ConcurrentHashMap<>();

    // Tiempo de deduplicación para conexiones (2 segundos)
    private static final long CONNECTION_DEDUPLICATION_WINDOW = 2000;

    /**
     * Verifica si una conexión de usuario es duplicada
     * Evita procesar múltiples conexiones para el mismo usuario en un corto período
     * de tiempo
     *
     * @param userId ID del usuario que se está conectando
     * @return true si es una conexión duplicada, false en caso contrario
     */
    private boolean isDuplicateConnection(Long userId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedConnections.put(userId, now);

        // Limpiar conexiones antiguas periódicamente
        if (processedConnections.size() > 100) {
            processedConnections.entrySet().removeIf(entry -> now - entry.getValue() > CONNECTION_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de
        // deduplicación
        return lastTime != null && now - lastTime < CONNECTION_DEDUPLICATION_WINDOW;
    }

    /**
     * Endpoint WebSocket para conectar un usuario
     */
    @MessageMapping("/user.connect")
    public void connectUser(Map<String, Object> payload) {
        // Validar el payload y extraer el userId
        Optional<Long> userIdOpt = extractUserIdForConnection(payload);

        if (userIdOpt.isEmpty()) {
            return; // Mensaje de log ya manejado en extractUserIdForConnection
        }

        Long userId = userIdOpt.get();

        // Obtener el flag de refresh
        boolean isRefresh = Optional.ofNullable(payload)
                .map(p -> p.get("isRefresh"))
                .map(value -> Boolean.TRUE.equals(value))
                .orElse(false);

        // Verificar si esta conexión ya fue procesada recientemente
        if (isDuplicateConnection(userId)) {
            log.debug("Conexión duplicada detectada para usuario: {}. Ignorando.", userId);
            return;
        }

        log.info("Solicitud WebSocket para conectar usuario: {}, isRefresh: {}", userId, isRefresh);

        // Procesar la conexión
        processUserConnection(userId, isRefresh);
    }

    /**
     * Extrae el ID de usuario del payload para conexión
     *
     * @param payload Payload con los datos del usuario
     * @return Optional con el ID de usuario, o empty si no es válido
     */
    private Optional<Long> extractUserIdForConnection(Map<String, Object> payload) {
        if (payload == null || !payload.containsKey("userId") || payload.get("userId") == null) {
            // log.info("Solicitud WebSocket para conectar usuario sin ID. Ignorando.");
            return Optional.empty();
        }

        Long userId = Long.valueOf(payload.get("userId").toString());

        if (userId == 0) {
            // log.info("Solicitud WebSocket para conectar usuario anónimo (ID=0).
            // Ignorando.");
            return Optional.empty();
        }

        return Optional.of(userId);
    }

    /**
     * Procesa la conexión de un usuario
     *
     * @param userId    ID del usuario
     * @param isRefresh Indica si es una reconexión por refresh
     */
    private void processUserConnection(Long userId, boolean isRefresh) {
        try {
            // Usar el método sobrecargado que acepta el parámetro isRefresh
            UserStatusDTO status = ((com.midas.crm.service.serviceImpl.UserConnectionServiceImpl) userConnectionService)
                    .connectUser(userId, isRefresh);
            messagingTemplate.convertAndSend("/topic/users/status", status);
        } catch (Exception e) {
            log.error("Error al conectar usuario {}: {}", userId, e.getMessage());
        }
    }

    // Mapa para evitar procesar desconexiones duplicadas
    private final Map<Long, Long> processedDisconnections = new ConcurrentHashMap<>();

    // Tiempo de deduplicación para desconexiones (5 segundos)
    private static final long DISCONNECT_DEDUPLICATION_WINDOW = 5000;

    /**
     * Verifica si una desconexión de usuario es duplicada
     * Evita procesar múltiples desconexiones para el mismo usuario en un corto
     * período de tiempo
     *
     * @param userId ID del usuario que se está desconectando
     * @return true si es una desconexión duplicada, false en caso contrario
     */
    private boolean isDuplicateDisconnection(Long userId) {
        long now = System.currentTimeMillis();
        Long lastTime = processedDisconnections.put(userId, now);

        // Limpiar desconexiones antiguas periódicamente
        if (processedDisconnections.size() > 100) {
            processedDisconnections.entrySet()
                    .removeIf(entry -> now - entry.getValue() > DISCONNECT_DEDUPLICATION_WINDOW);
        }

        // Es duplicado si ya existe y fue procesado hace menos del tiempo de
        // deduplicación para desconexiones
        return lastTime != null && now - lastTime < DISCONNECT_DEDUPLICATION_WINDOW;
    }

    /**
     * Endpoint WebSocket para desconectar un usuario
     */
    @MessageMapping("/user.disconnect")
    public void disconnectUser(Map<String, Object> payload) {
        // Validar el payload y extraer el userId
        Optional<Long> userIdOpt = extractUserId(payload);

        if (userIdOpt.isEmpty()) {
            return; // Mensaje de log ya manejado en extractUserId
        }

        Long userId = userIdOpt.get();

        // Verificar condiciones para procesar la desconexión
        if (shouldSkipDisconnection(userId)) {
            return; // Mensajes de log ya manejados en shouldSkipDisconnection
        }

        log.info("Solicitud WebSocket para desconectar usuario: {}", userId);
        try {
            // Desconectar al usuario (ignoramos el status retornado ya que no lo usamos)
            userConnectionService.disconnectUser(userId);

            // Obtener y enviar la lista completa de estados
            notifyUserDisconnection(userId);

            log.info("Usuario {} desconectado exitosamente", userId);
        } catch (Exception e) {
            log.error("Error al desconectar usuario {}: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * Extrae el ID de usuario del payload
     *
     * @param payload Payload con los datos del usuario
     * @return Optional con el ID de usuario, o empty si no es válido
     */
    private Optional<Long> extractUserId(Map<String, Object> payload) {
        if (payload == null || !payload.containsKey("userId") || payload.get("userId") == null) {
            log.warn("Solicitud WebSocket para desconectar usuario sin ID. Ignorando.");
            return Optional.empty();
        }

        Long userId = Long.valueOf(payload.get("userId").toString());

        if (userId == 0) {
            log.warn("Solicitud WebSocket para desconectar usuario anónimo (ID=0). Ignorando.");
            return Optional.empty();
        }

        return Optional.of(userId);
    }

    /**
     * Verifica si se debe omitir la desconexión
     *
     * @param userId ID del usuario
     * @return true si se debe omitir, false en caso contrario
     */
    private boolean shouldSkipDisconnection(Long userId) {
        // Verificar si el usuario ya está desconectado
        if (!userConnectionService.isUserConnected(userId)) {
            log.debug("Usuario {} ya está desconectado. Ignorando solicitud redundante.", userId);
            return true;
        }

        // Verificar si esta desconexión ya fue procesada recientemente
        if (isDuplicateDisconnection(userId)) {
            log.debug("Desconexión duplicada detectada para usuario: {}. Ignorando.", userId);
            return true;
        }

        return false;
    }

    /**
     * Notifica a todos los clientes sobre la desconexión de un usuario
     *
     * @param userId ID del usuario desconectado
     */
    private void notifyUserDisconnection(Long userId) {
        // Obtener la lista completa de estados para asegurar consistencia
        List<UserStatusDTO> allStatuses = userConnectionService.getAllUserStatuses();

        // Enviar solo la lista completa (evitar enviar actualizaciones individuales)
        messagingTemplate.convertAndSend("/topic/users/status/all", allStatuses);

        // Enviar una notificación específica de desconexión
        Map<String, Object> notification = new HashMap<>();
        notification.put("type", "USER_DISCONNECTED");
        notification.put("userId", userId);
        notification.put("status", "OFFLINE");
        notification.put("online", false);
        notification.put("timestamp", new Date());
        notification.put("payload", allStatuses); // Incluir la lista completa en la notificación
        messagingTemplate.convertAndSend("/topic/notifications", notification);
    }

    /**
     * Endpoint WebSocket para actualizar la actividad de un usuario
     */
    @MessageMapping("/user.activity")
    public void updateUserActivity(Map<String, Object> payload) {
        // Usar el mismo método de extracción de userId que usamos para la conexión
        extractUserIdForConnection(payload)
                .filter(userId -> userId > 0)
                .ifPresent(userId -> {
                    try {
                        userConnectionService.updateUserActivity(userId);
                    } catch (Exception e) {
                        // log.error("Error al actualizar actividad de usuario {}: {}", userId,
                        // e.getMessage());
                    }
                });
    }

    /**
     * Endpoint WebSocket para listar usuarios
     * Permite a los clientes solicitar la lista de usuarios en tiempo real con
     * paginación
     */
    @MessageMapping("/users.list")
    @SendTo("/topic/users/list")
    public Map<String, Object> listUsersWs(Map<String, Object> payload) {
        log.info("Solicitud WebSocket para listar usuarios con payload: {}", payload);

        try {
            // Extraer parámetros de paginación del payload
            PaginationParams params = extractPaginationParams(payload);

            // Obtener usuarios paginados y procesarlos
            return Optional
                    .ofNullable(userService.listUsers(params.page, params.size, params.sedeId, params.soloConectados))
                    .filter(response -> response.getBody() != null && response.getBody().getData() != null)
                    .map(response -> {
                        UserPageDTO pageData = response.getBody().getData();

                        // Convertir los UserDTO a UserResponseDTO
                        List<UserResponseDTO> userResponseDTOs = convertToResponseDTOs(pageData.getUsers());

                        // Crear y llenar el mapa de resultados
                        Map<String, Object> result = new HashMap<>();
                        result.put("users", userResponseDTOs);
                        result.put("totalItems", pageData.getTotalItems());
                        result.put("totalPages", pageData.getTotalPages());
                        result.put("currentPage", params.page);
                        result.put("pageSize", params.size);

                        // Incluir sessionId y requestId en la respuesta si están presentes
                        if (params.sessionId != null) {
                            result.put("sessionId", params.sessionId);
                        }
                        if (params.requestId != null) {
                            result.put("requestId", params.requestId);
                        }

                        log.info(
                                "Enviando respuesta paginada: {} usuarios, página {}/{}, total {}, sessionId: {}, requestId: {}",
                                userResponseDTOs.size(), params.page + 1, pageData.getTotalPages(),
                                pageData.getTotalItems(), params.sessionId, params.requestId);

                        return result;
                    })
                    .orElseGet(() -> {
                        // En caso de respuesta vacía
                        log.warn("No se encontraron usuarios o hubo un error en la respuesta");
                        return createEmptyResultMap(params.page, params.size, params.sessionId, params.requestId);
                    });
        } catch (Exception e) {
            log.error("Error al listar usuarios por WebSocket: {}", e.getMessage(), e);

            // En caso de excepción, devolver un objeto con lista vacía
            // Extraer sessionId y requestId del payload si es posible
            String sessionId = null;
            String requestId = null;
            if (payload != null) {
                if (payload.containsKey("sessionId")) {
                    sessionId = payload.get("sessionId").toString();
                }
                if (payload.containsKey("requestId")) {
                    requestId = payload.get("requestId").toString();
                }
            }

            Map<String, Object> errorResult = createEmptyResultMap(0, 10, sessionId, requestId);
            errorResult.put("error", "Error al procesar la solicitud: " + e.getMessage());

            return errorResult;
        }
    }

    /**
     * Clase para almacenar parámetros de paginación
     */
    private static class PaginationParams {
        int page;
        int size;
        Long sedeId;
        boolean soloConectados;
        String sessionId;
        String requestId;

        PaginationParams(int page, int size, Long sedeId, boolean soloConectados) {
            this.page = page;
            this.size = size;
            this.sedeId = sedeId;
            this.soloConectados = soloConectados;
        }
    }

    /**
     * Extrae los parámetros de paginación del payload
     *
     * @param payload Payload con los parámetros
     * @return Objeto con los parámetros de paginación
     */
    private PaginationParams extractPaginationParams(Map<String, Object> payload) {
        int page = 0;
        int size = 10;
        Long sedeId = null;
        boolean soloConectados = false;
        String sessionId = null;
        String requestId = null;

        if (payload != null) {
            if (payload.containsKey("page")) {
                page = Integer.parseInt(payload.get("page").toString());
                log.info("Página solicitada: {}", page);
            }
            if (payload.containsKey("size")) {
                size = Integer.parseInt(payload.get("size").toString());
                log.info("Tamaño solicitado: {}", size);
            }
            if (payload.containsKey("sede_id")) {
                sedeId = Long.valueOf(payload.get("sede_id").toString());
                log.info("Sede ID solicitada: {}", sedeId);
            }
            if (payload.containsKey("soloConectados")) {
                soloConectados = Boolean.parseBoolean(payload.get("soloConectados").toString());
                log.info("Solo conectados solicitado: {}", soloConectados);
            }
            if (payload.containsKey("sessionId")) {
                sessionId = payload.get("sessionId").toString();
                log.info("Session ID recibido: {}", sessionId);
            }
            if (payload.containsKey("requestId")) {
                requestId = payload.get("requestId").toString();
                log.info("Request ID recibido: {}", requestId);
            }
        }

        PaginationParams params = new PaginationParams(page, size, sedeId, soloConectados);
        params.sessionId = sessionId;
        params.requestId = requestId;
        return params;
    }

    /**
     * Convierte una lista de UserDTO a UserResponseDTO
     *
     * @param userDTOs Lista de UserDTO
     * @return Lista de UserResponseDTO
     */
    private List<UserResponseDTO> convertToResponseDTOs(List<UserDTO> userDTOs) {
        return userDTOs.stream()
                .map(userDTO -> {
                    UserResponseDTO responseDTO = new UserResponseDTO();
                    // Mapear propiedades básicas
                    responseDTO.setId(userDTO.getId());
                    responseDTO.setUsername(userDTO.getUsername());
                    responseDTO.setNombre(userDTO.getNombre());
                    responseDTO.setApellido(userDTO.getApellido());
                    responseDTO.setDni(userDTO.getDni());
                    responseDTO.setTelefono(userDTO.getTelefono());
                    responseDTO.setEmail(userDTO.getEmail());
                    responseDTO.setFechaCreacion(userDTO.getFechaCreacion());
                    responseDTO.setFechaCese(userDTO.getFechaCese());
                    responseDTO.setEstado(userDTO.getEstado());
                    responseDTO.setRole(userDTO.getRole());
                    responseDTO.setSede(userDTO.getSede());
                    responseDTO.setSede_id(userDTO.getSede_id());

                    // Mapear coordinador si existe
                    if (userDTO.getCoordinador() != null) {
                        // Crear un UserDTO a partir del UserCoordinadorDTO
                        UserDTO coordinadorDTO = new UserDTO();
                        coordinadorDTO.setId(userDTO.getCoordinador().getId());
                        coordinadorDTO.setUsername(userDTO.getCoordinador().getUsername());
                        coordinadorDTO.setNombre(userDTO.getCoordinador().getNombre());
                        coordinadorDTO.setApellido(userDTO.getCoordinador().getApellido());
                        coordinadorDTO.setDni(userDTO.getCoordinador().getDni());
                        coordinadorDTO.setTelefono(userDTO.getCoordinador().getTelefono());
                        coordinadorDTO.setEmail(userDTO.getCoordinador().getEmail());
                        coordinadorDTO.setSede(userDTO.getCoordinador().getSede());
                        coordinadorDTO.setSede_id(userDTO.getCoordinador().getSede_id());

                        responseDTO.setCoordinador(coordinadorDTO);
                    }

                    return responseDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * Crea un mapa de resultados vacío
     *
     * @param page Página actual
     * @param size Tamaño de página
     * @return Mapa con resultados vacíos
     */
    private Map<String, Object> createEmptyResultMap(int page, int size) {
        Map<String, Object> result = new HashMap<>();
        result.put("users", new ArrayList<>());
        result.put("totalItems", 0);
        result.put("totalPages", 0);
        result.put("currentPage", page);
        result.put("pageSize", size);
        return result;
    }

    /**
     * Crea un mapa de resultados vacío con sessionId y requestId
     *
     * @param page      Página actual
     * @param size      Tamaño de página
     * @param sessionId ID de sesión
     * @param requestId ID de solicitud
     * @return Mapa con resultados vacíos
     */
    private Map<String, Object> createEmptyResultMap(int page, int size, String sessionId, String requestId) {
        Map<String, Object> result = createEmptyResultMap(page, size);
        if (sessionId != null) {
            result.put("sessionId", sessionId);
        }
        if (requestId != null) {
            result.put("requestId", requestId);
        }
        return result;
    }

    /**
     * Endpoint WebSocket para buscar usuarios
     * Permite a los clientes buscar usuarios en tiempo real con paginación
     */
    @MessageMapping("/users.search")
    @SendTo("/topic/users/list")
    public Map<String, Object> searchUsersWs(Map<String, Object> payload) {
        log.info("Solicitud WebSocket para buscar usuarios con payload: {}", payload);

        try {
            // Extraer parámetros de búsqueda y paginación del payload
            SearchParams params = extractSearchParams(payload);

            // Obtener usuarios paginados mediante búsqueda y procesarlos
            return Optional.ofNullable(userService.searchUsers(params.query, params.page, params.size, params.sedeId))
                    .filter(response -> response.getBody() != null && response.getBody().getData() != null)
                    .map(response -> {
                        Map<String, Object> responseData = response.getBody().getData();

                        // Extraer y convertir la lista de usuarios
                        @SuppressWarnings("unchecked")
                        List<UserDTO> userDTOs = (List<UserDTO>) responseData.get("users");
                        List<UserResponseDTO> userResponseDTOs = convertToResponseDTOs(userDTOs);

                        // Crear y llenar el mapa de resultados
                        Map<String, Object> result = new HashMap<>();
                        result.put("users", userResponseDTOs);
                        result.put("totalItems", responseData.get("totalItems"));
                        result.put("totalPages", responseData.get("totalPages"));
                        result.put("currentPage", params.page);
                        result.put("pageSize", params.size);

                        // Incluir sessionId y requestId en la respuesta si están presentes
                        if (params.sessionId != null) {
                            result.put("sessionId", params.sessionId);
                        }
                        if (params.requestId != null) {
                            result.put("requestId", params.requestId);
                        }

                        log.info(
                                "Enviando respuesta de búsqueda paginada: {} usuarios, página {}/{}, total {}, sessionId: {}, requestId: {}",
                                userResponseDTOs.size(), params.page + 1, responseData.get("totalPages"),
                                responseData.get("totalItems"), params.sessionId, params.requestId);

                        return result;
                    })
                    .orElseGet(() -> {
                        // En caso de respuesta vacía
                        log.warn("No se encontraron usuarios o hubo un error en la respuesta de búsqueda");
                        return createEmptyResultMap(params.page, params.size, params.sessionId, params.requestId);
                    });
        } catch (Exception e) {
            log.error("Error al buscar usuarios por WebSocket: {}", e.getMessage(), e);

            // En caso de excepción, devolver un objeto con lista vacía
            // Extraer sessionId y requestId del payload si es posible
            String sessionId = null;
            String requestId = null;
            if (payload != null) {
                if (payload.containsKey("sessionId")) {
                    sessionId = payload.get("sessionId").toString();
                }
                if (payload.containsKey("requestId")) {
                    requestId = payload.get("requestId").toString();
                }
            }

            Map<String, Object> errorResult = createEmptyResultMap(0, 10, sessionId, requestId);
            errorResult.put("error", "Error al procesar la solicitud de búsqueda: " + e.getMessage());

            return errorResult;
        }
    }

    /**
     * Clase para almacenar parámetros de búsqueda
     */
    private static class SearchParams {
        int page;
        int size;
        Long sedeId;
        String query;
        String sessionId;
        String requestId;

        SearchParams(int page, int size, Long sedeId, String query) {
            this.page = page;
            this.size = size;
            this.sedeId = sedeId;
            this.query = query;
        }
    }

    /**
     * Extrae los parámetros de búsqueda del payload
     *
     * @param payload Payload con los parámetros
     * @return Objeto con los parámetros de búsqueda
     */
    private SearchParams extractSearchParams(Map<String, Object> payload) {
        int page = 0;
        int size = 10;
        Long sedeId = null;
        String query = null;
        String sessionId = null;
        String requestId = null;

        if (payload != null) {
            if (payload.containsKey("page")) {
                page = Integer.parseInt(payload.get("page").toString());
                log.info("Página solicitada: {}", page);
            }
            if (payload.containsKey("size")) {
                size = Integer.parseInt(payload.get("size").toString());
                log.info("Tamaño solicitado: {}", size);
            }
            if (payload.containsKey("sede_id")) {
                sedeId = Long.valueOf(payload.get("sede_id").toString());
                log.info("Sede ID solicitada: {}", sedeId);
            }
            if (payload.containsKey("query")) {
                query = payload.get("query").toString();
                log.info("Query de búsqueda: {}", query);
            }
            if (payload.containsKey("sessionId")) {
                sessionId = payload.get("sessionId").toString();
                log.info("Session ID recibido: {}", sessionId);
            }
            if (payload.containsKey("requestId")) {
                requestId = payload.get("requestId").toString();
                log.info("Request ID recibido: {}", requestId);
            }
        }

        SearchParams params = new SearchParams(page, size, sedeId, query);
        params.sessionId = sessionId;
        params.requestId = requestId;
        return params;
    }

    /**
     * Endpoint para verificar el estado de conexión de un usuario
     *
     * @param userId ID del usuario a verificar
     * @return true si el usuario está conectado, false en caso contrario
     */
    @GetMapping("/status/{userId}")
    public ResponseEntity<GenericResponse<Boolean>> isUserConnected(@PathVariable Long userId) {
        return Optional.of(userId)
                .map(id -> {
                    try {
                        boolean connected = userConnectionService.isUserConnected(id);
                        return ResponseBuilder.success(connected, "Estado de conexión obtenido exitosamente");
                    } catch (Exception e) {
                        log.error("Error al verificar estado de conexión: {}", e.getMessage());
                        throw new RuntimeException(e);
                    }
                })
                .orElseGet(() -> ResponseBuilder.error("ID de usuario inválido"));
    }
}
